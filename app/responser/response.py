from fastapi.responses import JSONResponse
from fastapi import Request
from typing import Any, Optional


class ResponseSuccess(JSONResponse):
    def __init__(self, message: str = None, data: Optional[Any] = None, code: int = 200):
        content = {
            "message": message,
            "code": code,
            "data": data
        }
        super().__init__(content=content, status_code=code)


class ResponseFailure(JSONResponse, Exception):
    def __init__(self, message: str = None, data: Optional[Any] = None, code: int = 400):
        self.message = message
        self.code = code
        self.data = data
        
        content = {
            "message": message,
            "code": code,
            "data": data
        }
        
        JSONResponse.__init__(self, content=content, status_code=code)
        Exception.__init__(self, message)


async def api_failure_handler(_: Request, error: ResponseFailure):
    return JSONResponse(
        content={"message": error.message, "code": error.code, "data": error.data},
        status_code=error.code
    )