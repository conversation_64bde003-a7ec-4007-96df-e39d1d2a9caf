from schemas.user.user import Delete<PERSON>ser as UserDelete
from ..cognito.cognito import CognitoService
from responser.response import ResponseSuccess , ResponseFailure
from botocore.exceptions import ClientError
from utils.logger import log_success, log_error 
from ..utils.validators import is_valid_email
class DeleteUser:
    def post(self, request: UserDelete):
        try:
            if not is_valid_email(request.email):
                return ResponseFailure(message="Please enter a valid email.", code=400)
            CognitoService.admin_delete_user(request.email.strip().lower())
            log_success(f"User {request.email} deleted successfully.")
            return ResponseSuccess(message="User deleted successfully", code=200)
        
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']


            if error_code == 'UserNotFoundException':
                log_error(f"User {request.email} does not exist", e)
                return ResponseFailure(message="User does not exist", code=404)
            elif error_code == 'InvalidParameterException':
                return ResponseFailure(message="Email must satisfy regular expression pattern: [\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]", code=500)
            else:
                log_error(f"ClientError in DeleteUser for user {request.email}", e)
                return ResponseFailure(message=str(e), code=500)
        except Exception as e:
            log_error(f"Unexpected error in DeleteUser for user {request.email}", e)
            return ResponseFailure(message=error_msg, code=status_code)