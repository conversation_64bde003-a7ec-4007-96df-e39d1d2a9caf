import boto3
from botocore.exceptions import ClientError
from schemas.user import user as UserSchema
from responser.response import ResponseFailure, ResponseSuccess
from core.config.environment import REGION
from services.cognito.cognito import CognitoService
from utils.logger import log_success , log_error
from ..utils.validators import is_valid_email , is_valid_password
from core.config.db import get_db
from models.user import User

cognito_client = boto3.client("cognito-idp", region_name=REGION)


class CreateAdminUser:
    def post(self, request: UserSchema.UserSignIn):
        try:
            if len(request.password.strip())==0:
                return ResponseFailure(message="Password can't be empty.",code=400)
            
            if not is_valid_email(request.email):
                return ResponseFailure(message="Please enter a valid email.", code=400)
            if not is_valid_password(request.password):
                return ResponseFailure(message="Please enter a valid password.", code=400)
            
            email = request.email.strip().lower()
            password = request.password.strip()

            #get firstname and last name from the database
            db_session = None
            try:
                db_session = next(get_db())
                row = db_session.query(User.userID,User.firstName,User.lastName).filter(User.emailAddress == email).first()
                if not row:
                    return ResponseFailure(message="User not found in database", code=404)
                first_name = ""
                last_name = ""
                if row is not None:
                    first_name = row[1]
                    last_name = row[2]
            except Exception as e:
                return ResponseFailure(message=f"Error getting user data from database.", code=500)
            
            finally:
                if db_session is not None:
                    db_session.close()

            response = CognitoService.admin_create_user(email, password, first_name,last_name)
            log_success(f"Cognito user created successfully: {response}")

            cognito_sub = next(attr["Value"] for attr in response["UserAttributes"] if attr["Name"] == "sub")

            return ResponseSuccess(
                message="User created successfully",
                code=200,
                data={"email": email, "cognito_id": cognito_sub}
            )

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']
            log_error(f"AWS Cognito error: {error_code} - {str(e)}")
            
            if error_code == "NotAuthorizedException":
                return ResponseFailure(message="Invalid email or password", code=401)
            elif error_code == "UsernameExistsException":
                return ResponseFailure(message="User already exists", code=409)
            elif error_code=="InvalidParameterException" and error_msg=="1 validation error detected: Value at 'temporaryPassword' failed to satisfy constraint: Member must satisfy regular expression pattern: ^[\\S]+.*[\\S]+$":
                return ResponseFailure(message="Password does not meet the required criteria." , code=status_code)
            elif error_code=="InvalidPasswordException":
                return ResponseFailure(message="Please enter a valid password" , code=500)
            else:
                return ResponseFailure(message=error_msg, code=status_code)

        except Exception as e:
            log_error(f"Unexpected error: {str(e)}")
            return ResponseFailure(message=str(e), code=500)

        

#for Saving to data base directly

# from services.cognito.cognito import CognitoService
# from models.user import User
# from core.config.db import get_db
# from schemas.user import user as UserSchema
# from botocore.exceptions import ClientError
# from responser.response import ResponseSuccess, ResponseFailure
# from utils.logger import log_success, log_error

# class CreateAdminUser:
#     def post(self, request: UserSchema.UserSignIn):
#         db_session = next(get_db())
#         log_success("Creating a new admin user...")

#         try:
#             email = request.email
#             password = request.password
            
#             # Set default values for firstName and lastName
#             first_name = "Lokesh"  
#             last_name = "Chauhan" 
#             company_name = "Nmg technologies"
            
#             log_success(f"Received signup request for email: {email}")

#             cognito_response = CognitoService.admin_create_user(email, password)
#             log_success(f"Cognito user created successfully: {cognito_response}")

#             # Extract Cognito user ID (sub)
#             cognito_user_id = next(attr['Value'] for attr in cognito_response["UserAttributes"] if attr["Name"] == "sub")
#             hashed_password = password  # Assuming password hashing is handled elsewhere
            
#             existing_user = db_session.query(User).filter(User.emailAddress == email).first()
            
#             if existing_user:
#                 existing_user.cognito_user_id = cognito_user_id
#                 existing_user.password = hashed_password
#                 existing_user.firstName = first_name  # Assign default first name
#                 existing_user.lastName = last_name   # Assign default last name
#                 existing_user.companyName = company_name  
#                 log_success(f"Updated existing user in DB: {email}")
#             else:
#                 new_user = User(
#                     emailAddress=email,
#                     password=hashed_password,
#                     cognito_user_id=cognito_user_id,
#                     firstName=first_name,  
#                     lastName=last_name,
#                     companyName = company_name,
#                     active="y"
#                 )
#                 db_session.add(new_user)
#                 log_success(f" New user created in DB: {email}")

#             db_session.commit()
#             log_success(f"User {email} created/updated successfully")
#             return ResponseSuccess(message="User created/updated successfully", code=200)

#         except Exception as e:
#             db_session.rollback()
#             log_error(f" Error creating/updating user {email}: {str(e)}")
            
#             try:
#                 response = CognitoService.admin_delete_user(request.email)
#                 log_success(f" Deleted user from Cognito: {response}")
#             except ClientError as delete_error:
#                 log_error(f" Failed to delete Cognito user {email}: {str(delete_error)}")
#                 return ResponseFailure(message=f"DB error: {str(e)}, Cognito deletion failed: {str(delete_error)}", code=500)
            
#             return ResponseFailure(message=f"DB error: {str(e)}, User deleted from Cognito", code=500)

#         finally:
#             db_session.close()
#             log_success(f"Closed DB session for {email}")

