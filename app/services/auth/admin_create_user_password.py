from fastapi import Request
from botocore.exceptions import ClientError
from responser.response import ResponseFailure, ResponseSuccess
from services.cognito.cognito import CognitoService
from utils.logger import log_success, log_error
from ..utils.validators import is_valid_password
from schemas.user.user import AdminChangeUserPassword as AdminChangePassSchema

class AdminChangeUserPassword:
    def post(self, request: Request, body: AdminChangePassSchema):
        try:
            email = body.email.strip()
            new_password = body.password.strip()

            if not email or not new_password:
                return ResponseFailure(message="Email and password are required.", code=400)

            if not is_valid_password(new_password):
                return ResponseFailure(message="Please enter a valid new password.", code=400)

            CognitoService.admin_change_password(email=email, password=new_password)

            log_success(f"Admin changed password successfully for user: {email}")
            return ResponseSuccess(message="Password changed successfully by admin", code=200)

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']

            if error_code == 'UserNotFoundException':
                return ResponseFailure(message="User does not exist", code=status_code)
            elif error_code == 'InvalidPasswordException':
                log_error("Invalid password format in admin password change", e)
                return ResponseFailure(message="Password does not meet complexity requirements", code=status_code)
            elif error_code == 'NotAuthorizedException':
                return ResponseFailure(message="You are not authorized to perform this action", code=status_code)
            else:
                log_error("ClientError in AdminChangeUserPassword", e)
                return ResponseFailure(message=error_msg, code=status_code)

        except Exception as e:
            log_error("Unexpected error in AdminChangeUserPassword", e)
            return ResponseFailure(message="Something went wrong while changing password", code=500)
