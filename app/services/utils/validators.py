import re

EMAIL_REGEX = re.compile(r"^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
# PASSWORD_REGEX = re.compile(r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!#%*?&]{8,}$")
PASSWORD_REGEX  = re.compile(r'^(?=.*[a-z])'r'(?=.*[A-Z])'r'(?=.*\d)'r'(?=.*[^\w\s])'r'.{8,}$')

def is_valid_email(email: str) -> bool:
    """
    Validates if the given email has a proper format.
    """
    if not email:
        return False
    return bool(EMAIL_REGEX.match(email.strip().lower()))


def is_valid_password(password: str) -> bool:
    """
    Validates if the given password meets security requirements:
    - At least 8 characters
    - At least 1 uppercase, 1 lowercase, 1 digit, and 1 special character
    """
    if not password:
        return False
    return bool(PASSWORD_REGEX.match(password.strip()))
