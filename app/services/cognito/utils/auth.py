from core.config.environment import APP_CLIENT_ID , CLIENT_SECRET
import hmac
import hashlib
import base64


def get_secret_hash(username: str) -> str:
    """Generate Cognito SECRET_HASH for user authentication"""
    message = username + APP_CLIENT_ID
    digest = hmac.new(
        key= CLIENT_SECRET.encode(),
        msg=message.encode(),
        digestmod=hashlib.sha256
    ).digest()
    return base64.b64encode(digest).decode()


