from sqlalchemy import Column, String, Enum, Date, TIMESTAMP
from sqlalchemy.dialects.mysql import INTEGER
from core.config.db import Base

class User(Base):
    __tablename__ = 'client_profile_users'

    userID = Column(INTEGER(unsigned=True), primary_key=True, autoincrement=True)
    password = Column(String(40), nullable=True)
    cognito_user_id = Column(String(60), nullable=True)
    firstName = Column(String(255), index=True)
    lastName = Column(String(255), index=True)
    emailAddress = Column(String(255), index=True, unique=True)
    companyName = Column(String(255), index=True)
    clientName = Column(String(255))
    streetAddress = Column(String(255))
    city = Column(String(255))
    state = Column(String(255))
    country = Column(String(255))
    zipCode = Column(String(15))
    phone = Column(String(30))
    ext = Column(String(3), nullable=True)
    fax = Column(String(30))
    IPAddress = Column(String(255), nullable=True)
    loginType = Column(Enum('U', 'P', 'A'), default='U')
    interestArea = Column(String(255), nullable=True)
    dateAdded = Column(Date, nullable=False, default='0000-00-00')
    active = Column(String(3), nullable=True, default='y')
    ipnotify = Column(INTEGER(unsigned=True), nullable=True, default=0)
    number_machines = Column(INTEGER(unsigned=True), default=1)
    bypass = Column(INTEGER(unsigned=True), default=1)
    plevel = Column(INTEGER(unsigned=True), nullable=True, default=0)
    is_public_user = Column(INTEGER(unsigned=True), nullable=True, default=0)
    ordering = Column(INTEGER(unsigned=True), nullable=True, default=0)
    reset_password_token = Column(String(500), nullable=True)
    insert_update_date = Column(TIMESTAMP, nullable=True, server_default='CURRENT_TIMESTAMP')
