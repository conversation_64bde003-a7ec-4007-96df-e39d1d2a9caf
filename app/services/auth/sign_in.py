from core.config.db import get_db
from schemas.user import user as UserSchema
from responser.response import ResponseFailure, ResponseSuccess
from services.cognito.cognito import CognitoService
from botocore.exceptions import ClientError
from utils.logger import log_success, log_error
from ..utils.validators import is_valid_email , is_valid_password
from models.user import User


class SignIn:
    def post(self, request: UserSchema.UserSignIn):
        """
        Signs in a user and returns tokens.
        Handles the NEW_PASSWORD_REQUIRED challenge if necessary.
        """
        try:
            if not is_valid_email(request.email):
                return ResponseFailure(message="Please enter a valid email.", code=400)
            if not is_valid_password(request.password):
                return ResponseFailure(message="Please enter a valid password.",code=400)
            email = request.email.strip().lower()
            password = request.password.strip()
            response = CognitoService.sign_in(email, password)
            if response.get('ChallengeName') == 'NEW_PASSWORD_REQUIRED' and response.get('Session'):
                session = response.get('Session')
                new_password = request.new_password
                if not new_password:
                    log_error("New password required for authentication but not provided.")
                    return ResponseFailure(message="New password is required to complete authentication.", code=400)
                if not is_valid_password(request.new_password):
                    return ResponseFailure(message="Please enter a valid new password.",code=400)
                try:
                    response = CognitoService.auth_challenge(email, new_password.strip() , session)
                    log_success(f"New password set successfully for user: {email}")
                    db = get_db()
                    db_session = next(db)
                    user = db_session.query(User).filter(User.emailAddress == email).first()
                    if user:
                        user.reset_password_token = None
                        # Update the password in the client_profile_users table
                        user.password = new_password
                        db_session.commit()
                    else:
                        log_error(f"User {email} not found in database during password reset.")
                        return ResponseFailure(message="User does not exist", code=404)
                    log_success(f"Password reset successful for {request.email}.")
                    db_session.close()
                except Exception as e:
                    log_error(f"Error in auth_challenge for user {email}: {e}")
                    return ResponseFailure(message=str(e), code=500)

            if "AuthenticationResult" not in response:
                log_error(f"Missing AuthenticationResult in Cognito response for user {email}")
                return ResponseFailure(message="Invalid authentication response", code=500)


            if response:
                db_session = None
                try:
                    db_session = next(get_db())
                    row = db_session.query(User.userID).filter(User.emailAddress == email).first()
                    if not row:
                        return ResponseFailure(message="User not found in database", code=404)
                    
                    user_id = row[0]

                except Exception as e:
                    log_error(f"Database error for user {email}: {e}")
                    return ResponseFailure(message=f"Failed to retrieve user data. Please try again later.", code=500)
                finally:
                    if db_session:
                        db_session.close()


            data = {
                "access_token": response["AuthenticationResult"].get("AccessToken"),
                "refresh_token": response["AuthenticationResult"].get("RefreshToken"),
                "user_id" : user_id,
                "user_email" : email
            }


            log_success(f"Login successful for user: {email}")
            return ResponseSuccess(message="Login Successful", data=data, code=200)

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']
            log_error(f"AWS Cognito error for user {email}: {error_code}", e)
            if error_code == 'NotAuthorizedException':
                return ResponseFailure(message="Invalid email or password", code=401)
            elif error_code == 'UserNotFoundException':
                return ResponseFailure(message="User does not exist", code=404)
            else:
                return ResponseFailure(message=error_msg, code=status_code)

        except Exception as e:
            log_error(f"Unexpected error during sign-in for user {email}: {e}")
            return ResponseFailure(message=str(e), code=500)
