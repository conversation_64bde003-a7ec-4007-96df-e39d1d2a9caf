from email.mime.text import <PERSON><PERSON><PERSON>ext
import smtplib
from core.config.environment import SENDER_EMAIL , SENDER_EMAIL_PASSWORD , SMTP_HOST_SERVER , SMTP_SERVER_PORT

def send_temp_password_email(to_email, temp_password):
    subject = "Your Temporary Password for Cognito"
    body = f"Hello,\n\nYour UserName is: {to_email}\nTemporary password is: {temp_password}\nPlease log in and change your password immediately.\n\nBest,\nSupport Team"

    msg = MIMEText(body)
    msg['From'] = SENDER_EMAIL
    msg['To'] = to_email
    msg['Subject'] = subject

    try:
        with smtplib.SMTP(SMTP_HOST_SERVER, SMTP_SERVER_PORT) as server:
            server.set_debuglevel(1)  # Enable debug output
            server.starttls()
            server.login(SENDER_EMAIL, SENDER_EMAIL_PASSWORD)
            server.sendmail(SENDER_EMAIL, to_email, msg.as_string())
        return True
    except smtplib.SMTPAuthenticationError as e:
        return False
    except Exception as e:
        return False
