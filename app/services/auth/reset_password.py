from schemas.user.user import ResetPass, ForgotPass
from responser.response import ResponseFailure, ResponseSuccess
from ..cognito.cognito import CognitoService
from botocore.exceptions import ClientError
from utils.logger import log_success, log_error
from ..utils.validators import is_valid_email , is_valid_password
from core.config.db import get_db
from models.user import User

class ForgotPassword:
    def post(self, request: ForgotPass):
        try:
            if not is_valid_email(request.email):
                return ResponseFailure(message="Please enter a valid email." , code=400)

            CognitoService.forgot_password(request.email.strip().lower())
            log_success(f"Password reset code sent to {request.email.strip().lower()}.")
            return ResponseSuccess(message="Code sent to your Email", code=200)
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'UserNotFoundException':
                log_error(f"User {request.email} does not exist", e)
                return ResponseFailure(message="User does not exist", code=404)
            elif error_code == 'NotAuthorizedException':
                return ResponseFailure(message="User does not exist", code=404)

            else:
                log_error(f"ClientError in ForgotPassword for user {request.email}", e)
                return ResponseFailure(message=str(e), code=500)
        except Exception as e:
            log_error(f"Unexpected error in ForgotPassword for user {request.email}", e)
            return ResponseFailure(message=str(e), code=500)

class ResetPassword:
    def post(self, request: ResetPass):
        """
        Resets password for a user using verification code.

        Args:
            request (ResetPass): Reset password request containing email, verification code and new password.

        Returns:
            ResponseSuccess: Password reset successful.
            ResponseFailure: ClientError or unexpected error occurred.
        """
        try:
            if not is_valid_email(request.email):
                return ResponseFailure(message="Please enter a valid email." , code=400)
            if not is_valid_password(request.new_password):
                return ResponseFailure(message="Please enter a valid password." , code=400)
            email  = request.email.strip().lower()
            code = request.code.strip()
            new_password = request.new_password.strip()
            CognitoService.reset_password(email, code , new_password)
            log_success(f"New password set successfully for user: {email}")
            db = get_db()
            db_session = next(db)
            user = db_session.query(User).filter(User.emailAddress == email).first()
            if user:
                user.reset_password_token = None
                # Update the password in the client_profile_users table
                user.password = new_password
                db_session.commit()
            else:
                log_error(f"User {email} not found in database during password reset.")
                return ResponseFailure(message="User does not exist", code=404)
            log_success(f"Password reset successful for {request.email}.")
            db_session.close()
            log_success(f"Password reset successfully for {request.email}.")
            return ResponseSuccess(message="Password reset successful", code=200)
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']

            if error_code == 'CodeMismatchException':
                log_error(f"Invalid verification code for {request.email}", e)
                return ResponseFailure(message="Invalid verification code", code=400)
            elif error_code == 'ExpiredCodeException':
                log_error(f"Verification code expired for {request.email}", e)
                return ResponseFailure(message="Invalid verification code", code=400)
            elif error_code == 'UserNotFoundException':
                log_error(f"User {request.email} does not exist", e)
                return ResponseFailure(message="User does not exist", code=404)
            elif error_code =="NotAuthorizedException":
                return ResponseFailure(message="User not authorized" , code=500)
            elif error_code=="LimitExceededException":
                return ResponseFailure(message="Attempt limit exceeded, please try after some time" , code=500)
            else:
                log_error(f"ClientError in ResetPassword for user {request.email}", e)
                return ResponseFailure(message=error_msg, code=status_code)
        except Exception as e:
            log_error(f"Unexpected error in ResetPassword for user {request.email}", e)
            return ResponseFailure(message=str(e), code=500)