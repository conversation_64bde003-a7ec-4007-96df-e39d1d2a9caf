############################################
# Dockerfile to build master microservice
# Based on python dockerhub image
############################################

# Set base image to python
#FROM 078398740737.dkr.ecr.us-west-2.amazonaws.com/repository-h1fytp6aeclu:07a51f81
FROM public.ecr.aws/docker/library/python:3.11.9-slim

# File Author / Maintainer
LABEL maintainer="Shamsher Kushwaha <<EMAIL>>"
ENV WORK_DIR=/usr/src/app
# Copy source file and python req's
COPY . /usr/src/
COPY app/requirements.txt /
RUN rm -rf $WORK_DIR/.env
COPY config/.env_prod $WORK_DIR/.env
# Install requirements
WORKDIR $WORK_DIR
RUN apt update
RUN apt install gcc build-essential python3-dev awscli curl  -y
RUN curl -SL https://ebs-snapper-078398740737.s3.us-east-1.amazonaws.com/mysql-tls-bundle.pem -o /srv/mysql-tls-bundle.pem
RUN python -m venv env
RUN python -m pip install --upgrade pip
RUN pip3 install -r requirements.txt

# Setup nginx
RUN curl https://nginx.org/keys/nginx_signing.key | gpg --dearmor \
    | tee /usr/share/keyrings/nginx-archive-keyring.gpg >/dev/null
RUN gpg --dry-run --quiet --no-keyring --import --import-options import-show /usr/share/keyrings/nginx-archive-keyring.gpg
RUN echo "deb [signed-by=/usr/share/keyrings/nginx-archive-keyring.gpg] \
http://nginx.org/packages/debian `lsb_release -cs` nginx" \
    | tee /etc/apt/sources.list.d/nginx.list
RUN apt update
RUN apt install nginx  -y
RUN rm /etc/nginx/conf.d/default.conf
ADD config/flask.conf /etc/nginx/conf.d/
RUN echo "daemon off;" >> /etc/nginx/nginx.conf

# Setup supervisord
RUN mkdir -p /usr/local/etc/supervisord/{conf-available,conf-enabled} /var/log/supervisor /var/log/supervisord /var/run/supervisord
ADD config/gunicorn_logging.conf .
ADD config/supervisord.conf /usr/local/etc/supervisord.conf
ADD config/nginx-supervisord.conf /usr/local/etc/supervisord/conf-enabled/
ADD config/gunicorn.conf /usr/local/etc/supervisord/conf-enabled/
COPY config/profile-prod.sh /srv/profile.sh 
ADD config/initial.sh /usr/bin/initial
RUN chmod +x /usr/bin/initial
ENTRYPOINT [ "initial" ]