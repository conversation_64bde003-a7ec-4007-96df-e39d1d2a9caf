import boto3
from botocore.config import Config
from botocore.exceptions import BotoCore<PERSON>rror, ClientError  
from core.config.environment import  APP_CLIENT_ID , REGION , USER_POOL_ID , AWS_PR0FILE
from .utils.auth import get_secret_hash
import time 
import csv
from io import <PERSON><PERSON>
from typing import List, Optional, Dict, Any


session = boto3.Session(profile_name=AWS_PR0FILE)
cognito_client = session.client("cognito-idp", region_name=REGION, config=Config(signature_version='v4'))


class CognitoService:

    @staticmethod
    def validate_token(access_token: str):
        """Validates the access token and returns user information."""
        response = cognito_client.get_user(AccessToken=access_token)
        return response

    @staticmethod
    def get_user(email:str):
        response = cognito_client.admin_get_user(
        UserPoolId=USER_POOL_ID,
        Username=email
        )
        return response
    
    @staticmethod
    def sign_up(email: str, password: str, name: str):
        secret_hash = get_secret_hash(email)
        response = cognito_client.sign_up(
            ClientId=APP_CLIENT_ID,
            Username=email,
            Password=password,
            SecretHash=secret_hash,
            UserAttributes=[{"Name": "email", "Value": email}, {"Name": "name", "Value": name}]
        )
        return response
    
    @staticmethod
    def confirm_email(email: str, code: str):
        """Confirms user email verification code."""
        response = cognito_client.confirm_sign_up(
            ClientId=APP_CLIENT_ID,
            Username=email,
            ConfirmationCode=code,
            SecretHash=get_secret_hash(email)

        )
        return response

    @staticmethod
    def sign_in(email: str, password: str ):
        """Authenticates a user and returns tokens."""
        response = cognito_client.initiate_auth(
            ClientId=APP_CLIENT_ID,
            AuthFlow="USER_PASSWORD_AUTH",
            AuthParameters={"USERNAME": email, "PASSWORD": password ,"SECRET_HASH": get_secret_hash(email) }
        )
    
        return response
    
    @staticmethod
    def sign_out(access_token: str):
        """Signs out a user from the current device/session."""
        response = cognito_client.global_sign_out(
            AccessToken=access_token
        )
        return response


    @staticmethod
    def forgot_password(email: str):
        """Sends password reset code to the user's email."""
        secret_hash = get_secret_hash(email)
        response = cognito_client.forgot_password(
            ClientId=APP_CLIENT_ID,
            Username=email,
            SecretHash=secret_hash
        )
        return response 

    @staticmethod
    def reset_password(email: str, code: str, new_password: str):
        """Resets password using verification code."""
        
        secret_hash = get_secret_hash(email)
        response = cognito_client.confirm_forgot_password(
            ClientId=APP_CLIENT_ID,
            Username=email,
            ConfirmationCode=code,
            Password=new_password,
            SecretHash = secret_hash

        )
        return response

    @staticmethod
    def change_password(access_token: str, old_password: str, new_password: str):
        """Changes password for authenticated users."""

        response = cognito_client.change_password(
            PreviousPassword=old_password,
            ProposedPassword=new_password,
            AccessToken=access_token
        )
        return response
    
    @staticmethod
    def admin_create_user(email: str, temporary_password: str, first_name:str,last_name:str,user_attributes: list = None):
        """Creates a new user in Cognito using admin privileges."""
        if user_attributes is None:
            user_attributes = [
                {'Name': 'email', 'Value': email},
                {'Name': 'email_verified', 'Value': 'true'},
                {'Name': 'given_name', 'Value': first_name},
                {'Name': 'family_name', 'Value': last_name}
            ]
        cognito_client.admin_create_user(
            UserPoolId=USER_POOL_ID,
            Username=email,
            TemporaryPassword=temporary_password,
            UserAttributes=user_attributes,
        #Commenting this will allow sending Mail to client
             MessageAction='SUPPRESS' 
        ) 
        #for uat and development start here --->
            # cognito_client.admin_set_user_password(
            # UserPoolId=USER_POOL_ID,
            # Username=email,
            # Password=temporary_password,
            # Permanent=True
            # )
        # for uat and development end here ---->
        response = cognito_client.admin_get_user(
        UserPoolId=USER_POOL_ID,
        Username=email
        )
        return response
    

    @staticmethod
    def admin_delete_user(email : str):
            response = CognitoService.get_user(email)
            if response:   
                response  = cognito_client.admin_delete_user(
                    UserPoolId=USER_POOL_ID,
                    Username=email
                )
            return response
        
    @staticmethod
    def auth_challenge(email:str , new_password:str , session:str):
        response  = cognito_client.respond_to_auth_challenge(
                        ClientId=APP_CLIENT_ID,
                        ChallengeName='NEW_PASSWORD_REQUIRED',
                        Session=session,
                        ChallengeResponses={
                            'USERNAME': email,
                            'NEW_PASSWORD': new_password,
                            'SECRET_HASH': get_secret_hash(email)
                        }
                    )
        return response

    @staticmethod
    def admin_change_password(email:str ,password:str):
        response = cognito_client.admin_set_user_password(
                                    UserPoolId=USER_POOL_ID,
                                    Username=email,
                                    Password=password,
                                    Permanent=True
                                )
        return response
    
    @staticmethod
    def admin_enable_user(email:str ,enable=str):
        """Enables or disables a user in the Cognito User Pool."""
        if enable == 'y':
            response = cognito_client.admin_enable_user(
                                        UserPoolId=USER_POOL_ID,
                                        Username=email
                                    )
        else:
            
            response = cognito_client.admin_disable_user(
                                        UserPoolId=USER_POOL_ID,
                                        Username=email
                                    )
            
        return response
#separate file for CSV export functionality   
class SmartCognitoCSV:
    
    @staticmethod
    def get_user_attribute(user: Dict, attribute_name: str) -> str:
        return next((attr["Value"] for attr in user.get("Attributes", []) if attr["Name"] == attribute_name), "")
    
    @staticmethod
    def smart_export(status_filter: Optional[str] = None, enabled_only: Optional[bool] = None, export_type: str = "basic") -> Dict[str, Any]:
        """
        Single method that handles both small and large datasets efficiently.
        Auto-detects size and chooses best approach.
        """
        start_time = time.time()
        
        # Quick estimation with first batch
        estimation_start = time.time()
        first_response = cognito_client.list_users(UserPoolId=USER_POOL_ID, Limit=60)
        estimation_time = time.time() - estimation_start
        
        total_in_sample = len(first_response.get("Users", []))
        has_more = bool(first_response.get("PaginationToken"))
        
        # Count filtered users in first batch
        filtered_sample = 0
        for user in first_response["Users"]:
            if status_filter and user["UserStatus"] != status_filter:
                continue
            if enabled_only is not None and user.get("Enabled") != enabled_only:
                continue
            filtered_sample += 1
        
        # Decide approach based on estimation
        if not has_more and total_in_sample <= 60:
            # Small dataset - use fast approach
            return SmartCognitoCSV._fast_approach(status_filter, enabled_only, export_type, first_response, start_time)
        elif filtered_sample == 0 and has_more:
            # Very sparse data - use optimized filtering
            return SmartCognitoCSV._sparse_approach(status_filter, enabled_only, export_type, start_time)
        else:
            # Medium/large dataset - use efficient batch approach
            return SmartCognitoCSV._batch_approach(status_filter, enabled_only, export_type, start_time)
    
    @staticmethod
    def smart_export_stream(status_filter: Optional[str] = None, enabled_only: Optional[bool] = None, export_type: str = "basic"):
        """
        Streaming version that yields CSV data progressively.
        Perfect for large datasets (80k+ users) to avoid timeouts.
        """
        # Write CSV header first
        if export_type == "basic":
            yield "email\n"
        elif export_type == "full":
            yield "email,status,enabled,created_date,last_modified\n"
        
        pagination_token = None
        batch_count = 0
        total_users = 0
        
        while True:
            try:
                batch_count += 1
                kwargs = {
                    "UserPoolId": USER_POOL_ID,
                    "Limit": 60  # AWS Cognito max limit per request
                }
                if pagination_token:
                    kwargs["PaginationToken"] = pagination_token
                
                response = cognito_client.list_users(**kwargs)
                users = response.get("Users", [])
                
                if not users:
                    break
                
                # Process users in current batch
                batch_csv_rows = []
                for user in users:
                    # Apply filters
                    if status_filter and user.get("UserStatus") != status_filter:
                        continue
                    if enabled_only is not None and user.get("Enabled") != enabled_only:
                        continue
                    
                    # Extract user data
                    email = SmartCognitoCSV.get_user_attribute(user, "email")
                    if not email:  # Skip users without email
                        continue
                    
                    total_users += 1
                    
                    if export_type == "basic":
                        # Escape quotes in email for CSV safety
                        escaped_email = email.replace('"', '""')
                        batch_csv_rows.append(f'"{escaped_email}"\n')
                    elif export_type == "full":
                        status = user.get("UserStatus", "")
                        enabled = user.get("Enabled", False)
                        created_date = user.get("UserCreateDate", "").strftime("%Y-%m-%d %H:%M:%S") if user.get("UserCreateDate") else ""
                        last_modified = user.get("UserLastModifiedDate", "").strftime("%Y-%m-%d %H:%M:%S") if user.get("UserLastModifiedDate") else ""
                        
                        # Escape quotes for CSV safety
                        escaped_email = email.replace('"', '""')
                        escaped_status = status.replace('"', '""')
                        
                        batch_csv_rows.append(f'"{escaped_email}","{escaped_status}","{enabled}","{created_date}","{last_modified}"\n')
                
                # Yield batch data if any rows were collected
                if batch_csv_rows:
                    yield "".join(batch_csv_rows)
                
                # Check for more pages
                pagination_token = response.get("PaginationToken")
                if not pagination_token:
                    break
                    
                # Optional: Add small delay to prevent overwhelming the API
                if batch_count % 10 == 0:  # Every 10 batches (600 users)
                    time.sleep(0.1)
                    
            except Exception as e:
                # Log error but continue with next batch instead of failing completely
                print(f"Error processing batch {batch_count}: {str(e)}")
                continue
    
    @staticmethod
    def _fast_approach(status_filter, enabled_only, export_type, first_response, start_time):
        """Fast approach for small datasets (≤60 users total)"""
        users = []
        
        # Process first batch
        for user in first_response["Users"]:
            if status_filter and user["UserStatus"] != status_filter:
                continue
            if enabled_only is not None and user.get("Enabled") != enabled_only:
                continue
            users.append(user)
        
        # Generate CSV
        csv_content = SmartCognitoCSV._generate_csv(users, export_type)
        total_time = time.time() - start_time
        
        return {
            "success": True,
            "csv_content": csv_content,
            "user_count": len(users),
            "total_time": round(total_time, 2),
            "approach": "fast",
            "api_calls": 1
        }
    
    @staticmethod
    def _batch_approach(status_filter, enabled_only, export_type, start_time):
        """Efficient batch approach for medium/large datasets"""
        users = []
        pagination_token = None
        api_calls = 0
        
        while True:
            api_calls += 1
            kwargs = {
                "UserPoolId": USER_POOL_ID,
                "Limit": 60
            }
            if pagination_token:
                kwargs["PaginationToken"] = pagination_token
                
            response = cognito_client.list_users(**kwargs)
            
            # Filter and collect users
            for user in response["Users"]:
                if status_filter and user["UserStatus"] != status_filter:
                    continue
                if enabled_only is not None and user.get("Enabled") != enabled_only:
                    continue
                users.append(user)
            
            pagination_token = response.get("PaginationToken")
            if not pagination_token:
                break
        
        # Generate CSV
        csv_content = SmartCognitoCSV._generate_csv(users, export_type)
        total_time = time.time() - start_time
        
        return {
            "success": True,
            "csv_content": csv_content,
            "user_count": len(users),
            "total_time": round(total_time, 2),
            "approach": "batch",
            "api_calls": api_calls
        }
    
    @staticmethod
    def _sparse_approach(status_filter, enabled_only, export_type, start_time):
        """Optimized approach for sparse filtered data"""
        users = []
        pagination_token = None
        api_calls = 0
        empty_batches = 0
        
        while True:
            api_calls += 1
            kwargs = {
                "UserPoolId": USER_POOL_ID,
                "Limit": 60
            }
            if pagination_token:
                kwargs["PaginationToken"] = pagination_token
                
            response = cognito_client.list_users(**kwargs)
            
            batch_matches = 0
            for user in response["Users"]:
                if status_filter and user["UserStatus"] != status_filter:
                    continue
                if enabled_only is not None and user.get("Enabled") != enabled_only:
                    continue
                users.append(user)
                batch_matches += 1
            
            # Early termination for very sparse data
            if batch_matches == 0:
                empty_batches += 1
                if empty_batches >= 5:  # Stop after 5 empty batches
                    break
            else:
                empty_batches = 0
            
            pagination_token = response.get("PaginationToken")
            if not pagination_token:
                break
        
        # Generate CSV
        csv_content = SmartCognitoCSV._generate_csv(users, export_type)
        total_time = time.time() - start_time
        
        return {
            "success": True,
            "csv_content": csv_content,
            "user_count": len(users),
            "total_time": round(total_time, 2),
            "approach": "sparse",
            "api_calls": api_calls
        }
    
    @staticmethod
    def _generate_csv(users: List[Dict], export_type: str) -> str:
        """Generate CSV content from users list"""
        buffer = StringIO()
        writer = csv.writer(buffer)
        
        if export_type == "basic":
            writer.writerow(["email"])
            for user in users:
                email = SmartCognitoCSV.get_user_attribute(user, "email")
                writer.writerow([email])
        elif export_type == "full":
            writer.writerow(["email", "status", "enabled"])
            for user in users:
                email = SmartCognitoCSV.get_user_attribute(user, "email")
                status = user.get("UserStatus", "")
                enabled = user.get("Enabled", False)
                writer.writerow([email, status, enabled])
        
        buffer.seek(0)
        return buffer.getvalue()