import os
from dotenv import load_dotenv
load_dotenv()

#for aws cognito
APP_CLIENT_ID = os.environ.get("CLIENT_PROFILE_APP_CLIENT_ID")
USER_POOL_ID = os.environ.get("CLIENT_PROFILE_USER_POOL_ID")
CLIENT_SECRET = os.getenv("CLIENT_PROFILE_CLIENT_SECRET")
REGION = os.getenv("REGION")
AWS_PR0FILE = os.getenv("AWS_PR0FILE")


#User db 
DB_USERNAME = os.environ.get('HISTORICAL_TLS_USERNAME')
DB_PASSWORD = os.environ.get('HISTORICAL_TLS_PASSWORD')
DB_HOST = os.environ.get('HISTORICAL_TLS_HOST')
DB_NAME = os.getenv("DB_NAME")
SSL_CA = os.getenv('GLOBAL_PEM_PATH')

# SSL_CA = os.getenv('GLOBAL_PEM_PATH')
# GLOBAL_PEM_PATH="/srv/mysql-tls-bundle.pem" 