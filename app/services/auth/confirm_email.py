from schemas.user.user import ConfirmEmail
from core.config.environment import REG<PERSON> , APP_CLIENT_ID
import boto3
from fastapi import Depends , HTTPException
from services.cognito.cognito import CognitoService
from responser.response import ResponseFailure , ResponseSuccess
from botocore.exceptions import ClientError
from utils.logger import log_success, log_error 
from ..utils.validators import is_valid_email

cognito_client = boto3.client("cognito-idp", region_name= REGION)

class ConfirmEmail:
    def post(self, request: ConfirmEmail):
        try:
            if not is_valid_email(request.email):
                return ResponseFailure(message="Please enter a valid email.", code=400)
            
            CognitoService.confirm_email(request.email.strip().lower(), request.code.strip()) 
            log_success(f"User {request.email} confirmed successfully.")
            return ResponseSuccess(message="User confirmed successfully. You can now log in.",code=200)
        
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']

            if error_code == 'CodeMismatchException':
                log_error(f"Invalid confirmation code for user {request.email}", e)
                return ResponseFailure(message="Invalid confirmation code",code=400)
            elif error_code == 'ExpiredCodeException':
                log_error(f"Confirmation code expired for user {request.email}", e)
                return ResponseFailure(message="Confirmation code expired", code=400)
            elif error_code == 'NotAuthorizedException':
                log_error(f"User {request.email} already confirmed", e)
                return ResponseFailure(message="User already confirmed", code=401)
            else:
                log_error(f"ClientError in ConfirmEmail for user {request.email}", e)
                return ResponseFailure(message=error_msg, code=status_code)

        except Exception as e:
            log_error(f"Unexpected error in ConfirmEmail for user {request.email}", e)
            return ResponseFailure(message=str(e), code=500)
