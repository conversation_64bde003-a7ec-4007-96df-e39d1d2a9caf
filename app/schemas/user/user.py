from pydantic import BaseModel , EmailStr 
from typing import Optional

class UserSignUp(BaseModel):
    email: str
    name: str
    password :str


class UserSignIn(BaseModel):
    email: str
    password: str
    new_password : Optional[str] = None 


class ConfirmEmail(BaseModel):
    email : str
    code : str


class ChangePassword(BaseModel):
    old_password : str
    new_password : str

class DeleteUser(BaseModel):
    email : str

class ResetPass(BaseModel):
    email : str
    code : str
    new_password : str

class ForgotPass(BaseModel):
    email : str

class GetUser(BaseModel):
    email : str

class AdminChangeUserPassword(BaseModel):
    email : str
    password : str

class AdminUserStatus(BaseModel):
    email : str
    status : str