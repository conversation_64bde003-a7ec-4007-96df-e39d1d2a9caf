from schemas.user.user import GetUser
from responser.response import ResponseSuccess , ResponseFailure
from ..cognito.cognito import CognitoService
from botocore.exceptions import ClientError
from utils.logger import log_success, log_error
from ..utils.validators import is_valid_email

class GetUserDetails:
    def post(self, request: GetUser):
        try:
            if not is_valid_email(request.email):
                return ResponseFailure(message="Please enter a valid email.", code=400)
            response = CognitoService.get_user(request.email.strip().lower())
            log_success(f"User details fetched successfully for {request.email}.")
            
            data = {
                "Username": response["Username"],
                "EmailVerified": next((attr["Value"] for attr in response["UserAttributes"] if attr["Name"] == "email_verified"), "false"),
                "Sub": next((attr["Value"] for attr in response["UserAttributes"] if attr["Name"] == "sub"), None),
                "UserStatus": response.get("UserStatus", "UNKNOWN")
            }
            
            return ResponseSuccess(message="User details fetched successfully", data=data, code=200)
        
        except ClientError as e:    
            error_code = e.response['Error']['Code']
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']

            if error_code == 'UserNotFoundException':
                log_error(f"User {request.email} does not exist", e)
                return ResponseFailure(message="User does not exist", code=404)
            else:
                log_error(f"ClientError in GetUserDetails for user {request.email}", e)
                return ResponseFailure(message=error_msg, code=500)
        except Exception as e:
            log_error(f"Unexpected error in GetUserDetails for user {request.email}", e)
            return ResponseFailure(message=str(e), code=500)