from schemas.user.user import ChangePassword as ChangePass
from services.cognito.cognito import CognitoService
from responser.response import ResponseFailure, ResponseSuccess
from fastapi import Request
from botocore.exceptions import ClientError
from utils.logger import log_success, log_error 
from ..utils.validators import is_valid_password

class ChangePassword:
    def post(self, request: Request, body: ChangePass):
        try:
            token = request.headers.get("Authorization")
            
            old_pass = body.old_password.strip()
            new_pass = body.new_password.strip()

            

            if len(old_pass) ==0 or len(new_pass)==0:
                return ResponseFailure(message="Password can't be empty.",code=400)
            
            if not token:
                log_error("Token missing in ChangePassword request")
                return ResponseFailure(message="Token missing", code=401)
            
            if not is_valid_password(old_pass):
                return ResponseFailure(message="Please enter a valid password.", code=400)
            
            if not is_valid_password(new_pass):
                return ResponseFailure(message="Please enter a valid new password.", code=400)
            
            CognitoService.change_password(token, old_pass, new_pass)
            log_success("Password changed successfully for user")
            return ResponseSuccess(message="Password changed successfully", code=200)
        
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']

            if error_code == 'NotAuthorizedException' and error_msg=='Incorrect username or password.':
                return ResponseFailure(message="Incorrect Password", code=status_code)
            elif error_code == 'InvalidPasswordException':
                log_error("Please enter a valid new password.", e)
                return ResponseFailure(message="Please enter a valid new password.", code=status_code)
            elif error_code=='LimitExceededException':
                return ResponseFailure(message=error_msg, code=status_code)
            else:
                log_error("ClientError in ChangePassword", e)
                return ResponseFailure(message=error_msg, code=status_code)
        except Exception as e:
            log_error("Unexpected error in ChangePassword", e)
            return ResponseFailure(message=str(e), code=500)