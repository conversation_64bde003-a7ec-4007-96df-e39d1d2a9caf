import boto3
from fastapi import Request
from botocore.exceptions import ClientError
from core.config.environment import REGION
from responser.response import ResponseFailure, ResponseSuccess
from services.cognito.cognito import CognitoService
from utils.logger import log_success, log_error
from schemas.user.user import AdminUserStatus as AdminUserStatusSchema

cognito_client = boto3.client("cognito-idp", region_name=REGION)

class AdminUserStatus:
    def post(self, request: Request, body: AdminUserStatusSchema):
        try:
            email = body.email.strip().lower()
            status = body.status.strip()
            if not email or not status:
                return ResponseFailure(message="Email and status are required.", code=400)
            # status = True if status == "y" else False
            
            CognitoService.admin_enable_user(email=email, enable=status)

            log_success(f"Admin changed status successfully for user: {email}")
            return ResponseSuccess(message="User Status changed successfully by admin", code=200)

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']

            if error_code == 'UserNotFoundException':
                return ResponseFailure(message="User does not exist", code=status_code)
            
            elif error_code == 'NotAuthorizedException':
                return ResponseFailure(message="You are not authorized to perform this action", code=status_code)
            else:
                log_error("ClientError in AdminChangeUserPassword", e)
                return ResponseFailure(message=error_msg, code=status_code)

        except Exception as e:
            log_error("Unexpected error in AdminChangeUserPassword", e)
            return ResponseFailure(message="Something went wrong while changing password", code=500)
