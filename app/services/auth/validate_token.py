from fastapi import Request
from responser.response import ResponseFailure, ResponseSuccess
from botocore.exceptions import ClientError
from utils.logger import log_error
from services.cognito.cognito import CognitoService


class ValidateToken:
    def get(self, request: Request):
        try:
            token = request.headers.get("Authorization")
            if not token:
                log_error("Token missing in validation request.")
                return ResponseFailure(message="Authorization token is required.", code=400)

            if token.lower().startswith("bearer "):
                token = token[7:]
            token = token.strip()

            # Validate the token
            response = CognitoService.validate_token(token)

            if not response:
                log_error("Token validation failed: No response from Cognito.")
                return ResponseFailure(message=False, code=401)

            return ResponseSuccess(message=True, code=200)

        except ClientError as e:
            error_code = e.response['Error']['Code']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']
            if status_code==400 and error_code=='NotAuthorizedException':
                return ResponseFailure(message=False, code=401)
            return ResponseFailure(message=False, code=500)

        except Exception as e:
            log_error("Unexpected error in ValidateToken", e)
            return ResponseFailure(message=False, code=500)
