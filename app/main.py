from fastapi import FastAP<PERSON>
from contextlib import asynccontextmanager
import uvicorn
from routes.get_route_map import routing
from core.config.db import Base
from core.config.db import engine
from fastapi.middleware.cors import CORSMiddleware


Base.metadata.create_all(bind=engine)

app = FastAPI() 

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  
    allow_credentials=True,
    allow_methods=["*"], 
    allow_headers=["*"],
)

routing(app)  


# if __name__ == "__main__":
#     uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
