from fastapi import Query
from fastapi.responses import StreamingResponse
from typing import Optional
from botocore.exceptions import ClientError
from fastapi_utils.cbv import cbv
from fastapi_utils.inferring_router import InferringRouter
from responser.response import ResponseFailure
from services.cognito.cognito import SmartCognitoCSV
from utils.logger import log_success, log_error

router = InferringRouter()

@cbv(router)
class DownloadUsersCSV:
    """Smart download service that handles both small and large datasets automatically with streaming support."""

    @router.get("/")
    def get(
        self,
        status_filter: Optional[str] = Query(None, description="Filter by status: CONFIRMED, UNCONFIRMED, etc."),
        enabled_only: Optional[bool] = Query(None, description="true for enabled users"),
        export_type: str = Query("basic", description="Export type: 'basic' or 'full'"),
        filename: Optional[str] = Query(None, description="Custom filename (without .csv)"),
        use_streaming: Optional[bool] = Query(True, description="Use streaming for large datasets (recommended for 1000+ users)")
    ):
        try:
            log_success(f"CSV download request: status={status_filter}, enabled={enabled_only}, type={export_type}, streaming={use_streaming}")

            if export_type not in ["basic", "full"]:
                return ResponseFailure(message="Invalid export_type. Use 'basic' or 'full'.", code=400)

            valid_statuses = [
                "CONFIRMED", "UNCONFIRMED", "FORCE_CHANGE_PASSWORD", 
                "RESET_REQUIRED", "ARCHIVED", "COMPROMISED", "UNKNOWN"
            ]
            if status_filter and status_filter not in valid_statuses:
                return ResponseFailure(
                    message=f"Invalid status_filter. Must be one of: {', '.join(valid_statuses)}", code=400
                )

            # Generate filename
            filename_parts = []
            if filename:
                filename_parts.append(filename)
            else:
                filename_parts.append("users")
                if status_filter:
                    filename_parts.append(status_filter.lower())
                if enabled_only is not None:
                    filename_parts.append("enabled" if enabled_only else "disabled")
                if export_type != "basic":
                    filename_parts.append(export_type)
                    
            filename_str = "_".join(filename_parts) + ".csv"

            # Decide whether to use streaming based on parameter
            if use_streaming:
                # Use streaming approach - perfect for large datasets (80k+ users)
                log_success("Using streaming approach for CSV export")
                
                return StreamingResponse(
                    SmartCognitoCSV.smart_export_stream(
                        status_filter=status_filter,
                        enabled_only=enabled_only,
                        export_type=export_type
                    ),
                    media_type="text/csv",
                    headers={
                        "Content-Disposition": f"attachment; filename={filename_str}",
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "X-Export-Type": export_type,
                        "X-Stream-Mode": "enabled",
                        "X-Approach": "streaming"
                    }
                )
            else:
                # Use traditional approach - good for smaller datasets
                log_success("Using traditional approach for CSV export")
                
                result = SmartCognitoCSV.smart_export(
                    status_filter=status_filter,
                    enabled_only=enabled_only,
                    export_type=export_type
                )

                if not result["success"]:
                    log_error("CSV export failed")
                    return ResponseFailure(message="Export failed", code=500)

                log_success(f"Export completed: {result['user_count']} users in {result['total_time']}s using {result['approach']} approach")

                return StreamingResponse(
                    iter([result["csv_content"]]),
                    media_type="text/csv",
                    headers={
                        "Content-Disposition": f"attachment; filename={filename_str}",
                        "X-Total-Users": str(result["user_count"]),
                        "X-Export-Type": export_type,
                        "X-Processing-Time": f"{result['total_time']}s",
                        "X-Approach": result["approach"],
                        "X-API-Calls": str(result["api_calls"]),
                        "X-Efficiency": f"{result['user_count']/result['total_time']:.1f} users/sec" if result['total_time'] > 0 else "instant"
                    }
                )

        except ClientError as e:
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']
            log_error(f"Cognito error: {error_msg}")
            return ResponseFailure(message=f"Cognito error: {error_msg}", code=status_code)

        except Exception as e:
            log_error(f"Unexpected error: {str(e)}")
            return ResponseFailure(message="Unexpected server error", code=500)