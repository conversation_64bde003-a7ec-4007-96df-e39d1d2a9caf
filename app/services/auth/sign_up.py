from schemas.user import user as UserSchema
from responser.response import ResponseFailure, ResponseSuccess
from services.cognito.cognito import CognitoService
from botocore.exceptions import ClientError
from utils.logger import log_success, log_error
from ..utils.validators import is_valid_email , is_valid_password



class SignUp:
    def post(self, request: UserSchema.UserSignUp):

        try:
            if not is_valid_email(request.email):
                return ResponseFailure(message="Please enter a valid email.",code=400)
            if not is_valid_password(request.password):
                return ResponseFailure(message="Please enter a valid password.",code=400)
            response = CognitoService.sign_up(request.email.strip().lower(), request.password.strip(), request.name.strip())
            log_success(f"User {request.email} registered successfully.")

            if response:
                data = {"email": request.email, "cognito_id": response['UserSub'] , "confirmed" : response['UserConfirmed']}
            return ResponseSuccess(message="User registered successfully. Please verify your email.", data=data, code=200)
        
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']

            if error_code == 'UsernameExistsException':
                log_error(f"User {request.email} already exists", e)
                return ResponseFailure(message="User already exists", code=400)
            elif error_code == 'InvalidPasswordException':
                log_error(f"Password does not meet security requirements for user {request.email}", e)
                return ResponseFailure(message="Password does not meet security requirements", code=400)
            else:
                log_error(f"ClientError in SignUp for user {request.email}", e)
                return ResponseFailure(message=error_msg, code=status_code)
        except Exception as e:
            log_error(f"Unexpected error in SignUp for user {request.email}", e)
            return ResponseFailure(message=str(e), code=500)