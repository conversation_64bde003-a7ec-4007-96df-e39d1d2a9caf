[loggers]
keys=root, gunicorn.error, gunicorn.access, requests.packages.urllib3.connectionpool, __main__

[handlers]
keys=log_file, syslog

[formatters]
keys=generic

[logger_root]
level=INFO
handlers=log_file, syslog

[logger___main__]
level=DEBUG
handlers=log_file, syslog
propagate=0
qualname=__main__

[logger_gunicorn.error]
level=INFO
handlers=log_file, syslog
propagate=0
qualname=gunicorn.error

[logger_gunicorn.access]
level=INFO
handlers=log_file, syslog
propagate=0
qualname=gunicorn.access

[logger_requests.packages.urllib3.connectionpool]
level=WARN
handlers=log_file, syslog
propagate=0
qualname=requests.packages.urllib3.connectionpool

[handler_syslog]
class=logging.handlers.SysLogHandler
formatter=generic
args=()

[handler_log_file]
class=logging.FileHandler
formatter=generic
args=('/dev/stdout',)

[formatter_generic]
format=%(asctime)s [%(process)d:%(name)s:%(lineno)s] [%(levelname)s] %(message)s
datefmt=%Y-%m-%d %H:%M:%S
class=logging.Formatter