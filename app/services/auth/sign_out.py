from fastapi import Request
from utils.logger import log_error 
from responser.response import ResponseFailure, ResponseSuccess
from services.cognito.cognito import CognitoService
from botocore.exceptions import ClientError


class SignOut:
    async def post(self, request: Request):
        try:
            token = request.headers.get("Authorization")
            if not token:
                log_error("Token missing in SignOut request")
                return ResponseFailure(message="Token missing", code=401)

            if token.lower().startswith("bearer "):
                token = token[7:]

            CognitoService.sign_out(token.strip())
            
            return ResponseSuccess(message="Signed out successfull", code=200)
        except ClientError as e:
            error_msg = e.response['Error']['Message']
            status_code = e.response['ResponseMetadata']['HTTPStatusCode']
            return ResponseFailure(message=error_msg , code=status_code)
        except Exception as e:
            log_error(f"Sign-out error: {str(e)}")
            return ResponseFailure(message=f"Sign-out error: {str(e)}", code=500)
