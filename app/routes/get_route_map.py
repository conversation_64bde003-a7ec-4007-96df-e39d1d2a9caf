from fastapi import Depends
from fastapi_utils import Api
from services.auth.admin_user_status import AdminUserStatus
from services.auth.sign_up import SignUp  
from services.home import Home
from services.auth.sign_in import SignIn 
from services.auth.confirm_email import ConfirmEmail
from services.auth.change_password import ChangePassword
from services.auth.admin_create_user import CreateAdminUser
from services.auth.delete_user import DeleteUser
from services.auth.reset_password import ResetPassword , ForgotPassword
from services.auth.get_user import GetUserDetails
from services.auth.admin_create_user_password import AdminChangeUserPassword
from services.auth.sign_out import SignOut
from services.auth.download_users_csv import DownloadUsersCSV
from services.auth.validate_token import ValidateToken



# This file defines the route map for the application, including all routes and their dependencies.
secured_routes: list[str] = []


def get_route_map():
    return [
        {
            "router": Home(),
            "path": "/",
            "dependencies": [],
        },
        {
            "router": SignIn(),
            "path": "/sign-in-aws",
            "dependencies": [],
        },
        {
            "router": SignOut(),
            "path": "/sign-out-aws",
            "dependencies": [],
        },
        {
            "router": SignUp(),
            "path": "/sign-up-aws",
            "dependencies": [],
        },
        {
            "router": ConfirmEmail(),
            "path": "/confirm-email", 
            "dependencies": [],
        },
        {
            "router": ChangePassword(),
            "path": "/change-password", 
            "dependencies": [],
        },
        {
            "router":ResetPassword(),
            "path": "/reset-password", 
            "dependencies": [],
        },
        {
            "router":ForgotPassword(),
            "path": "/forgot-password", 
            "dependencies": [],
        },
        {
            "router": CreateAdminUser(),
            "path": "/create-admin-user", 
            "dependencies": [],
        },
        {
            "router": DeleteUser(),
            "path": "/delete-user", 
            "dependencies": [],
        },
        {
            "router": GetUserDetails(),
            "path": "/get-user-details", 
            "dependencies": [],
        },
        {
            "router": AdminChangeUserPassword(),
            "path": "/admin-change-password", 
            "dependencies": [],
        },
        {
            "router": AdminUserStatus(),
            "path": "/change-user-status", 
            "dependencies": [],
        },
        {
            "router": DownloadUsersCSV(),
            "path": "/download-users-csv", 
            "dependencies": [],
        },
        {
            "router": ValidateToken(),
            "path": "/validate-token", 
            "dependencies": [],
        },
    ]


def routing(app):
    route_map = get_route_map()
    for obj in route_map:
        dec_list = obj.get("dependencies", [])
        api = Api(app)
        api.add_resource(
            obj["router"], obj["path"], dependencies=[Depends(d) for d in dec_list]
        )
        if obj.get("requiredAuth") == True:
            secured_routes.append(obj["path"])

